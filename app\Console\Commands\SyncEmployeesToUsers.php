<?php

namespace App\Console\Commands;

use App\Models\Employee;
use App\Models\User;
use Illuminate\Console\Command;

class SyncEmployeesToUsers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'employees:sync-to-users';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sincronizza i dipendenti esistenti con la tabella users per permettere il login';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Inizio sincronizzazione dipendenti con tabella users...');

        $employees = Employee::all();
        $syncedCount = 0;
        $skippedCount = 0;

        foreach ($employees as $employee) {
            // Controlla se esiste già un utente con questa email
            $existingUser = User::where('email', $employee->email)->first();

            if ($existingUser) {
                $this->warn("Utente già esistente per: {$employee->email}");
                $skippedCount++;
                continue;
            }

            // Crea l'utente corrispondente
            User::create([
                'name' => $employee->nome . ' ' . $employee->cognome,
                'email' => $employee->email,
                'password' => $employee->password, // Usa la password già hashata del dipendente
                'role' => 'Dipendente',
                'foto' => $employee->foto,
            ]);

            $this->info("Creato utente per: {$employee->nome} {$employee->cognome} ({$employee->email})");
            $syncedCount++;
        }

        $this->info("Sincronizzazione completata!");
        $this->info("Utenti creati: {$syncedCount}");
        $this->info("Utenti già esistenti (saltati): {$skippedCount}");

        return Command::SUCCESS;
    }
}
