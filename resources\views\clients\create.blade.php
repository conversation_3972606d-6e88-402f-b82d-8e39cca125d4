@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-6">
            <h1 class="h3 mb-0">Nuovo Cliente</h1>
            <p class="text-muted">Aggiungi un nuovo cliente all'agenzia</p>
        </div>
        <div class="col-md-6 text-end">
            <a href="{{ route('clients.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Torna alla lista
            </a>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Informazioni Cliente</h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('clients.store') }}" method="POST">
                        @csrf
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="nome" class="form-label">Nome/Ragione Sociale *</label>
                                    <input type="text" class="form-control @error('nome') is-invalid @enderror" 
                                           id="nome" name="nome" value="{{ old('nome') }}" required>
                                    @error('nome')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="cognome" class="form-label">Cognome</label>
                                    <input type="text" class="form-control @error('cognome') is-invalid @enderror" 
                                           id="cognome" name="cognome" value="{{ old('cognome') }}">
                                    <div class="form-text">Obbligatorio solo per persone fisiche</div>
                                    @error('cognome')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="tipo" class="form-label">Tipo Cliente *</label>
                            <select class="form-select @error('tipo') is-invalid @enderror" id="tipo" name="tipo" required>
                                <option value="">Seleziona tipo cliente</option>
                                <option value="Persona Fisica" {{ old('tipo') == 'Persona Fisica' ? 'selected' : '' }}>Persona Fisica</option>
                                <option value="Persona Giuridica" {{ old('tipo') == 'Persona Giuridica' ? 'selected' : '' }}>Persona Giuridica</option>
                            </select>
                            @error('tipo')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email" class="form-label">Email</label>
                                    <input type="email" class="form-control @error('email') is-invalid @enderror" 
                                           id="email" name="email" value="{{ old('email') }}">
                                    @error('email')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="telefono" class="form-label">Telefono</label>
                                    <input type="tel" class="form-control @error('telefono') is-invalid @enderror" 
                                           id="telefono" name="telefono" value="{{ old('telefono') }}">
                                    @error('telefono')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="indirizzo" class="form-label">Indirizzo</label>
                            <textarea class="form-control @error('indirizzo') is-invalid @enderror" 
                                      id="indirizzo" name="indirizzo" rows="2">{{ old('indirizzo') }}</textarea>
                            @error('indirizzo')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="partita_iva" class="form-label">Partita IVA</label>
                                    <input type="text" class="form-control @error('partita_iva') is-invalid @enderror" 
                                           id="partita_iva" name="partita_iva" value="{{ old('partita_iva') }}">
                                    @error('partita_iva')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="codice_fiscale" class="form-label">Codice Fiscale</label>
                                    <input type="text" class="form-control @error('codice_fiscale') is-invalid @enderror" 
                                           id="codice_fiscale" name="codice_fiscale" value="{{ old('codice_fiscale') }}">
                                    @error('codice_fiscale')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="note" class="form-label">Note</label>
                            <textarea class="form-control @error('note') is-invalid @enderror" 
                                      id="note" name="note" rows="3">{{ old('note') }}</textarea>
                            <div class="form-text">Note aggiuntive sul cliente</div>
                            @error('note')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ route('clients.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Annulla
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Salva Cliente
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-hide cognome field for Persona Giuridica
document.getElementById('tipo').addEventListener('change', function() {
    const cognomeField = document.getElementById('cognome').closest('.col-md-6');
    const cognomeInput = document.getElementById('cognome');
    
    if (this.value === 'Persona Giuridica') {
        cognomeField.style.display = 'none';
        cognomeInput.removeAttribute('required');
    } else {
        cognomeField.style.display = 'block';
        if (this.value === 'Persona Fisica') {
            cognomeInput.setAttribute('required', 'required');
        }
    }
});

// Trigger on page load if tipo is already selected
if (document.getElementById('tipo').value) {
    document.getElementById('tipo').dispatchEvent(new Event('change'));
}
</script>
@endsection
