@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-6">
            <h1 class="h3 mb-0">Gestione Tasks</h1>
            <p class="text-muted">
                @if(auth()->user()->isAdmin())
                    Gestisci tutti i tasks dell'agenzia
                @else
                    I tuoi tasks assegnati
                @endif
            </p>
        </div>
        <div class="col-md-6 text-end">
            @if(auth()->user()->isAdmin())
                <a href="{{ route('tasks.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Nuovo Task
                </a>
            @endif
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="{{ route('tasks.index') }}" class="row g-3">
                <div class="col-md-3">
                    <label for="search" class="form-label">Cerca</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="{{ request('search') }}" placeholder="Titolo, descrizione...">
                </div>
                <div class="col-md-2">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">Tutti</option>
                        <option value="Assegnato" {{ request('status') == 'Assegnato' ? 'selected' : '' }}>Assegnato</option>
                        <option value="In Corso" {{ request('status') == 'In Corso' ? 'selected' : '' }}>In Corso</option>
                        <option value="Completato" {{ request('status') == 'Completato' ? 'selected' : '' }}>Completato</option>
                        <option value="Scaduto" {{ request('status') == 'Scaduto' ? 'selected' : '' }}>Scaduto</option>
                    </select>
                </div>
                @if(auth()->user()->isAdmin())
                <div class="col-md-3">
                    <label for="project_id" class="form-label">Progetto</label>
                    <select class="form-select" id="project_id" name="project_id">
                        <option value="">Tutti i progetti</option>
                        @foreach($projects as $project)
                            <option value="{{ $project->id }}" {{ request('project_id') == $project->id ? 'selected' : '' }}>
                                {{ $project->titolo }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="user_id" class="form-label">Dipendente</label>
                    <select class="form-select" id="user_id" name="user_id">
                        <option value="">Tutti</option>
                        @foreach($users as $user)
                            <option value="{{ $user->id }}" {{ request('user_id') == $user->id ? 'selected' : '' }}>
                                {{ $user->name }}
                            </option>
                        @endforeach
                    </select>
                </div>
                @endif
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-outline-primary me-2">
                        <i class="fas fa-search"></i> Filtra
                    </button>
                    <a href="{{ route('tasks.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i> Reset
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Tasks Table -->
    <div class="card">
        <div class="card-body">
            @if($tasks->count() > 0)
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Titolo</th>
                                <th>Progetto</th>
                                @if(auth()->user()->isAdmin())
                                    <th>Assegnato a</th>
                                @endif
                                <th>Status</th>
                                <th>Scadenza</th>
                                <th>Azioni</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($tasks as $task)
                                <tr class="{{ $task->isOverdue() ? 'table-danger' : '' }}">
                                    <td>
                                        <strong>{{ $task->titolo }}</strong>
                                        @if($task->descrizione)
                                            <br><small class="text-muted">{{ Str::limit($task->descrizione, 50) }}</small>
                                        @endif
                                    </td>
                                    <td>
                                        <a href="{{ route('projects.show', $task->project) }}" class="text-decoration-none">
                                            {{ $task->project->titolo }}
                                        </a>
                                    </td>
                                    @if(auth()->user()->isAdmin())
                                        <td>{{ $task->user->name }}</td>
                                    @endif
                                    <td>
                                        <span class="badge {{ $task->getStatusBadgeClass() }}">
                                            {{ $task->status }}
                                        </span>
                                    </td>
                                    <td>
                                        {{ $task->data_scadenza->format('d/m/Y') }}
                                        @if($task->isOverdue())
                                            <br><small class="text-danger">
                                                <i class="fas fa-exclamation-triangle"></i> Scaduto
                                            </small>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('tasks.show', $task) }}" class="btn btn-sm btn-outline-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('tasks.edit', $task) }}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            @if(auth()->user()->isAdmin())
                                                <form action="{{ route('tasks.destroy', $task) }}" method="POST" class="d-inline">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-sm btn-outline-danger" 
                                                            onclick="return confirm('Sei sicuro di voler eliminare questo task?')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-center">
                    {{ $tasks->links() }}
                </div>
            @else
                <div class="text-center py-4">
                    <i class="fas fa-tasks fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Nessun task trovato</h5>
                    <p class="text-muted">
                        @if(auth()->user()->isAdmin())
                            Non ci sono tasks che corrispondono ai criteri di ricerca.
                        @else
                            Non hai tasks assegnati al momento.
                        @endif
                    </p>
                </div>
            @endif
        </div>
    </div>
</div>

<style>
.badge.bg-secondary { background-color: #6c757d !important; }
.badge.bg-primary { background-color: #0d6efd !important; }
.badge.bg-success { background-color: #198754 !important; }
.badge.bg-danger { background-color: #dc3545 !important; }
</style>
@endsection
