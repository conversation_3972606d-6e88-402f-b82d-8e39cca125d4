<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('clients', function (Blueprint $table) {
            $table->id();
            $table->string('nome'); // First name or company name
            $table->string('cognome')->nullable(); // Last name (nullable for companies)
            $table->enum('tipo', ['Persona Fisica', 'Persona Giuridica']); // Individual or Company
            $table->string('email')->nullable(); // Email contact
            $table->string('telefono')->nullable(); // Phone contact
            $table->string('indirizzo')->nullable(); // Address
            $table->string('partita_iva')->nullable(); // VAT number for companies
            $table->string('codice_fiscale')->nullable(); // Tax code
            $table->text('note')->nullable(); // Additional notes
            $table->timestamps();
            $table->softDeletes(); // For soft delete functionality
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('clients');
    }
};
