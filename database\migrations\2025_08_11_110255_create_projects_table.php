<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('projects', function (Blueprint $table) {
            $table->id();
            $table->foreignId('client_id')->constrained()->onDelete('cascade'); // Link to client
            $table->string('titolo'); // Project title
            $table->text('tipologia_lavoro'); // Work type description
            $table->enum('tipo_progetto', ['One-shot', 'Ricorrente']); // Project type
            $table->date('data_scadenza')->nullable(); // Deadline for one-shot projects
            $table->string('frequenza')->nullable(); // Frequency for recurring projects (e.g., "Settimanale", "Mensile")
            $table->decimal('compenso', 10, 2); // Compensation/fee
            $table->enum('stato', ['In Attesa', 'In Corso', 'Completato', 'Annullato'])->default('In Attesa'); // Project status
            $table->text('descrizione')->nullable(); // Additional description
            $table->text('note')->nullable(); // Internal notes
            $table->timestamps();
            $table->softDeletes(); // For soft delete functionality
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('projects');
    }
};
