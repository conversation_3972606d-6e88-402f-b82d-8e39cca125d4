<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-6">
            <h1 class="h3 mb-0">Gestione Tasks</h1>
            <p class="text-muted">
                <?php if(auth()->user()->isAdmin()): ?>
                    Gestisci tutti i tasks dell'agenzia
                <?php else: ?>
                    I tuoi tasks assegnati
                <?php endif; ?>
            </p>
        </div>
        <div class="col-md-6 text-end">
            <?php if(auth()->user()->isAdmin()): ?>
                <a href="<?php echo e(route('tasks.create')); ?>" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Nuovo Task
                </a>
            <?php endif; ?>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="<?php echo e(route('tasks.index')); ?>" class="row g-3">
                <div class="col-md-3">
                    <label for="search" class="form-label">Cerca</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="<?php echo e(request('search')); ?>" placeholder="Titolo, descrizione...">
                </div>
                <div class="col-md-2">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">Tutti</option>
                        <option value="Assegnato" <?php echo e(request('status') == 'Assegnato' ? 'selected' : ''); ?>>Assegnato</option>
                        <option value="In Corso" <?php echo e(request('status') == 'In Corso' ? 'selected' : ''); ?>>In Corso</option>
                        <option value="Completato" <?php echo e(request('status') == 'Completato' ? 'selected' : ''); ?>>Completato</option>
                        <option value="Scaduto" <?php echo e(request('status') == 'Scaduto' ? 'selected' : ''); ?>>Scaduto</option>
                    </select>
                </div>
                <?php if(auth()->user()->isAdmin()): ?>
                <div class="col-md-3">
                    <label for="project_id" class="form-label">Progetto</label>
                    <select class="form-select" id="project_id" name="project_id">
                        <option value="">Tutti i progetti</option>
                        <?php $__currentLoopData = $projects; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $project): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($project->id); ?>" <?php echo e(request('project_id') == $project->id ? 'selected' : ''); ?>>
                                <?php echo e($project->titolo); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="user_id" class="form-label">Dipendente</label>
                    <select class="form-select" id="user_id" name="user_id">
                        <option value="">Tutti</option>
                        <?php $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($user->id); ?>" <?php echo e(request('user_id') == $user->id ? 'selected' : ''); ?>>
                                <?php echo e($user->name); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>
                <?php endif; ?>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-outline-primary me-2">
                        <i class="fas fa-search"></i> Filtra
                    </button>
                    <a href="<?php echo e(route('tasks.index')); ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i> Reset
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Tasks Table -->
    <div class="card">
        <div class="card-body">
            <?php if($tasks->count() > 0): ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Titolo</th>
                                <th>Progetto</th>
                                <?php if(auth()->user()->isAdmin()): ?>
                                    <th>Assegnato a</th>
                                <?php endif; ?>
                                <th>Status</th>
                                <th>Scadenza</th>
                                <th>Azioni</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $tasks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $task): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr class="<?php echo e($task->isOverdue() ? 'table-danger' : ''); ?>">
                                    <td>
                                        <strong><?php echo e($task->titolo); ?></strong>
                                        <?php if($task->descrizione): ?>
                                            <br><small class="text-muted"><?php echo e(Str::limit($task->descrizione, 50)); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <a href="<?php echo e(route('projects.show', $task->project)); ?>" class="text-decoration-none">
                                            <?php echo e($task->project->titolo); ?>

                                        </a>
                                    </td>
                                    <?php if(auth()->user()->isAdmin()): ?>
                                        <td><?php echo e($task->user->name); ?></td>
                                    <?php endif; ?>
                                    <td>
                                        <span class="badge <?php echo e($task->getStatusBadgeClass()); ?>">
                                            <?php echo e($task->status); ?>

                                        </span>
                                    </td>
                                    <td>
                                        <?php echo e($task->data_scadenza->format('d/m/Y')); ?>

                                        <?php if($task->isOverdue()): ?>
                                            <br><small class="text-danger">
                                                <i class="fas fa-exclamation-triangle"></i> Scaduto
                                            </small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?php echo e(route('tasks.show', $task)); ?>" class="btn btn-sm btn-outline-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="<?php echo e(route('tasks.edit', $task)); ?>" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <?php if(auth()->user()->isAdmin()): ?>
                                                <form action="<?php echo e(route('tasks.destroy', $task)); ?>" method="POST" class="d-inline">
                                                    <?php echo csrf_field(); ?>
                                                    <?php echo method_field('DELETE'); ?>
                                                    <button type="submit" class="btn btn-sm btn-outline-danger" 
                                                            onclick="return confirm('Sei sicuro di voler eliminare questo task?')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-center">
                    <?php echo e($tasks->links()); ?>

                </div>
            <?php else: ?>
                <div class="text-center py-4">
                    <i class="fas fa-tasks fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Nessun task trovato</h5>
                    <p class="text-muted">
                        <?php if(auth()->user()->isAdmin()): ?>
                            Non ci sono tasks che corrispondono ai criteri di ricerca.
                        <?php else: ?>
                            Non hai tasks assegnati al momento.
                        <?php endif; ?>
                    </p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
.badge.bg-secondary { background-color: #6c757d !important; }
.badge.bg-primary { background-color: #0d6efd !important; }
.badge.bg-success { background-color: #198754 !important; }
.badge.bg-danger { background-color: #dc3545 !important; }
</style>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Lavori\36k\gestionale\laravel\resources\views/tasks/index.blade.php ENDPATH**/ ?>