<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Project extends Model
{
    use SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'client_id',
        'titolo',
        'tipologia_lavoro',
        'tipo_progetto',
        'data_scadenza',
        'frequenza',
        'compenso',
        'stato',
        'descrizione',
        'note',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'data_scadenza' => 'date',
            'compenso' => 'decimal:2',
        ];
    }

    /**
     * Get the client that owns the project.
     */
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    /**
     * Get the tasks for the project.
     */
    public function tasks(): Has<PERSON><PERSON>
    {
        return $this->hasMany(Task::class);
    }

    /**
     * Check if the project is recurring.
     */
    public function isRecurring(): bool
    {
        return $this->tipo_progetto === 'Ricorrente';
    }

    /**
     * Check if the project is one-shot.
     */
    public function isOneShot(): bool
    {
        return $this->tipo_progetto === 'One-shot';
    }
}
