<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('employees', function (Blueprint $table) {
            $table->id();
            $table->string('nome'); // First name
            $table->string('cognome'); // Last name
            $table->enum('ruolo', ['Fotografo', 'Editor Video', 'Programmatore Web', 'Social Media Manager']); // Role
            $table->string('foto')->nullable(); // Photo path
            $table->string('telefono'); // Phone number
            $table->string('email')->unique(); // Email address
            $table->timestamp('email_verified_at')->nullable();
            $table->string('password'); // Generated password
            $table->rememberToken();
            $table->timestamps();
            $table->softDeletes(); // For soft delete functionality
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('employees');
    }
};
