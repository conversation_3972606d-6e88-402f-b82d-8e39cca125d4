@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-6">
            <h1 class="h3 mb-0">Nuovo Dipendente</h1>
            <p class="text-muted">Aggiungi un nuovo dipendente all'agenzia</p>
        </div>
        <div class="col-md-6 text-end">
            <a href="{{ route('employees.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Torna alla lista
            </a>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Informazioni Dipendente</h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('employees.store') }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="nome" class="form-label">Nome *</label>
                                    <input type="text" class="form-control @error('nome') is-invalid @enderror" 
                                           id="nome" name="nome" value="{{ old('nome') }}" required>
                                    @error('nome')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="cognome" class="form-label">Cognome *</label>
                                    <input type="text" class="form-control @error('cognome') is-invalid @enderror" 
                                           id="cognome" name="cognome" value="{{ old('cognome') }}" required>
                                    @error('cognome')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="ruolo" class="form-label">Ruolo *</label>
                                    <select class="form-select @error('ruolo') is-invalid @enderror" id="ruolo" name="ruolo" required>
                                        <option value="">Seleziona un ruolo</option>
                                        <option value="Fotografo" {{ old('ruolo') == 'Fotografo' ? 'selected' : '' }}>Fotografo</option>
                                        <option value="Editor Video" {{ old('ruolo') == 'Editor Video' ? 'selected' : '' }}>Editor Video</option>
                                        <option value="Programmatore Web" {{ old('ruolo') == 'Programmatore Web' ? 'selected' : '' }}>Programmatore Web</option>
                                        <option value="Social Media Manager" {{ old('ruolo') == 'Social Media Manager' ? 'selected' : '' }}>Social Media Manager</option>
                                    </select>
                                    @error('ruolo')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="telefono" class="form-label">Telefono *</label>
                                    <input type="tel" class="form-control @error('telefono') is-invalid @enderror" 
                                           id="telefono" name="telefono" value="{{ old('telefono') }}" required>
                                    @error('telefono')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="email" class="form-label">Email *</label>
                            <input type="email" class="form-control @error('email') is-invalid @enderror"
                                   id="email" name="email" value="{{ old('email') }}" required>
                            @error('email')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="role" class="form-label">Ruolo Sistema *</label>
                            <select class="form-select @error('role') is-invalid @enderror" id="role" name="role" required>
                                <option value="">Seleziona un ruolo sistema</option>
                                <option value="Administrator" {{ old('role') == 'Administrator' ? 'selected' : '' }}>Administrator</option>
                                <option value="Dipendente" {{ old('role') == 'Dipendente' ? 'selected' : '' }}>Dipendente</option>
                            </select>
                            <div class="form-text">Determina i permessi di accesso al sistema</div>
                            @error('role')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="foto" class="form-label">Foto Profilo</label>
                            <input type="file" class="form-control @error('foto') is-invalid @enderror" 
                                   id="foto" name="foto" accept="image/*">
                            <div class="form-text">Formati supportati: JPEG, PNG, JPG, GIF. Dimensione massima: 2MB</div>
                            @error('foto')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            <strong>Nota:</strong> Una password sicura verrà generata automaticamente per il nuovo dipendente e mostrata dopo la creazione.
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ route('employees.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Annulla
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Salva Dipendente
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Preview uploaded image
document.getElementById('foto').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            // Create preview if it doesn't exist
            let preview = document.getElementById('foto-preview');
            if (!preview) {
                preview = document.createElement('img');
                preview.id = 'foto-preview';
                preview.className = 'mt-2 rounded';
                preview.style.maxWidth = '150px';
                preview.style.maxHeight = '150px';
                document.getElementById('foto').parentNode.appendChild(preview);
            }
            preview.src = e.target.result;
        };
        reader.readAsDataURL(file);
    }
});
</script>
@endsection
