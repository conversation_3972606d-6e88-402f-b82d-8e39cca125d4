<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Task;
use App\Models\Project;
use App\Models\User;
use Illuminate\Support\Facades\Auth;

class TaskController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Task::with(['project', 'user']);

        // If user is not admin, show only their assigned tasks
        if (!Auth::user()->isAdmin()) {
            $query->where('user_id', Auth::id());
        }

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('titolo', 'like', "%{$search}%")
                  ->orWhere('descrizione', 'like', "%{$search}%")
                  ->orWhereHas('project', function ($projectQuery) use ($search) {
                      $projectQuery->where('titolo', 'like', "%{$search}%");
                  });
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by project (admin only)
        if ($request->filled('project_id') && Auth::user()->isAdmin()) {
            $query->where('project_id', $request->project_id);
        }

        // Filter by assigned user (admin only)
        if ($request->filled('user_id') && Auth::user()->isAdmin()) {
            $query->where('user_id', $request->user_id);
        }

        // Update overdue tasks
        $this->updateOverdueTasks();

        $tasks = $query->latest()->paginate(10)->withQueryString();

        // Get data for filters (admin only)
        $projects = Auth::user()->isAdmin() ? Project::orderBy('titolo')->get() : collect();
        $users = Auth::user()->isAdmin() ? User::where('role', 'Dipendente')->orderBy('name')->get() : collect();

        return view('tasks.index', compact('tasks', 'projects', 'users'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request)
    {
        // Only admin can create tasks
        if (!Auth::user()->isAdmin()) {
            abort(403, 'Solo gli amministratori possono creare tasks.');
        }

        $projects = Project::orderBy('titolo')->get();
        $users = User::where('role', 'Dipendente')->orderBy('name')->get();
        $selectedProject = $request->project_id ? Project::find($request->project_id) : null;

        return view('tasks.create', compact('projects', 'users', 'selectedProject'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Only admin can create tasks
        if (!Auth::user()->isAdmin()) {
            abort(403, 'Solo gli amministratori possono creare tasks.');
        }

        $validated = $request->validate([
            'project_id' => 'required|exists:projects,id',
            'user_id' => 'required|exists:users,id',
            'titolo' => 'required|string|max:255',
            'descrizione' => 'nullable|string|max:2000',
            'status' => 'required|in:Assegnato,In Corso,Completato',
            'data_scadenza' => 'required|date|after:today',
        ]);

        // Verify that the assigned user is an employee
        $user = User::find($validated['user_id']);
        if (!$user->isEmployee()) {
            return back()->withErrors(['user_id' => 'Il task può essere assegnato solo a dipendenti.']);
        }

        Task::create($validated);

        return redirect()->route('tasks.index')
            ->with('success', 'Task creato con successo.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Task $task)
    {
        // Check authorization
        if (!Auth::user()->isAdmin() && $task->user_id !== Auth::id()) {
            abort(403, 'Non hai i permessi per visualizzare questo task.');
        }

        $task->load(['project', 'user']);
        return view('tasks.show', compact('task'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Task $task)
    {
        // Check authorization
        if (!Auth::user()->isAdmin() && $task->user_id !== Auth::id()) {
            abort(403, 'Non hai i permessi per modificare questo task.');
        }

        $projects = Project::orderBy('titolo')->get();
        $users = User::where('role', 'Dipendente')->orderBy('name')->get();

        return view('tasks.edit', compact('task', 'projects', 'users'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Task $task)
    {
        // Check authorization
        if (!Auth::user()->isAdmin() && $task->user_id !== Auth::id()) {
            abort(403, 'Non hai i permessi per modificare questo task.');
        }

        $rules = [
            'titolo' => 'required|string|max:255',
            'descrizione' => 'nullable|string|max:2000',
            'status' => 'required|in:Assegnato,In Corso,Completato',
            'data_scadenza' => 'required|date',
        ];

        // Admin can change project and assigned user
        if (Auth::user()->isAdmin()) {
            $rules['project_id'] = 'required|exists:projects,id';
            $rules['user_id'] = 'required|exists:users,id';
        }

        $validated = $request->validate($rules);

        // Verify that the assigned user is an employee (admin only)
        if (Auth::user()->isAdmin() && isset($validated['user_id'])) {
            $user = User::find($validated['user_id']);
            if (!$user->isEmployee()) {
                return back()->withErrors(['user_id' => 'Il task può essere assegnato solo a dipendenti.']);
            }
        }

        $task->update($validated);

        return redirect()->route('tasks.index')
            ->with('success', 'Task aggiornato con successo.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Task $task)
    {
        // Only admin can delete tasks
        if (!Auth::user()->isAdmin()) {
            abort(403, 'Solo gli amministratori possono eliminare tasks.');
        }

        $task->delete();

        return redirect()->route('tasks.index')
            ->with('success', 'Task eliminato con successo.');
    }

    /**
     * Update overdue tasks status.
     */
    private function updateOverdueTasks()
    {
        Task::where('data_scadenza', '<', now())
            ->whereNotIn('status', ['Completato'])
            ->update(['status' => 'Scaduto']);
    }
}
