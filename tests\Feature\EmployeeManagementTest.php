<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class EmployeeManagementTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create an admin user for testing
        $this->admin = User::factory()->create([
            'role' => 'Administrator',
            'email' => '<EMAIL>'
        ]);
    }

    public function test_admin_can_access_employee_index(): void
    {
        $response = $this->actingAs($this->admin)->get('/employees');
        $response->assertStatus(200);
        $response->assertSee('Gestione Dipendenti');
    }

    public function test_admin_can_create_employee(): void
    {
        $employeeData = [
            'nome' => 'Mario',
            'cognome' => 'Rossi',
            'ruolo' => 'Fotografo',
            'telefono' => '123456789',
            'email' => '<EMAIL>',
        ];

        $response = $this->actingAs($this->admin)->post('/employees', $employeeData);

        $response->assertRedirect('/employees');
        $this->assertDatabaseHas('employees', [
            'email' => '<EMAIL>',
            'nome' => 'Mario',
            'cognome' => 'Rossi'
        ]);
    }

    public function test_non_admin_cannot_access_employee_management(): void
    {
        $employee = User::factory()->create(['role' => 'Dipendente']);

        $response = $this->actingAs($employee)->get('/employees');
        $response->assertStatus(403);
    }

    public function test_dashboard_shows_statistics_for_admin(): void
    {
        $response = $this->actingAs($this->admin)->get('/dashboard');

        $response->assertStatus(200);
        $response->assertSee('Dashboard');
        $response->assertSee('Dipendenti');
        $response->assertSee('Clienti');
        $response->assertSee('Progetti');
    }
}
