@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-6">
            <h1 class="h3 mb-0">Gestione Clienti</h1>
            <p class="text-muted">Gestisci i clienti dell'agenzia</p>
        </div>
        <div class="col-md-6 text-end">
            <a href="{{ route('clients.create') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Nuovo Cliente
            </a>
        </div>
    </div>

    <!-- Search and Filter -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="{{ route('clients.index') }}">
                <div class="row">
                    <div class="col-md-4">
                        <input type="text" name="search" class="form-control" placeholder="Cerca per nome, email, telefono, P.IVA..." value="{{ request('search') }}">
                    </div>
                    <div class="col-md-3">
                        <select name="tipo" class="form-select">
                            <option value="">Tutti i tipi</option>
                            <option value="Persona Fisica" {{ request('tipo') == 'Persona Fisica' ? 'selected' : '' }}>Persona Fisica</option>
                            <option value="Persona Giuridica" {{ request('tipo') == 'Persona Giuridica' ? 'selected' : '' }}>Persona Giuridica</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <button type="submit" class="btn btn-outline-primary">
                            <i class="fas fa-search"></i> Cerca
                        </button>
                        <a href="{{ route('clients.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i> Reset
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Clients Table -->
    <div class="card">
        <div class="card-body">
            @if($clients->count() > 0)
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Nome/Ragione Sociale</th>
                                <th>Tipo</th>
                                <th>Contatti</th>
                                <th>Progetti</th>
                                <th>Azioni</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($clients as $client)
                            <tr>
                                <td>
                                    <strong>{{ $client->full_name }}</strong>
                                    @if($client->partita_iva)
                                        <br><small class="text-muted">P.IVA: {{ $client->partita_iva }}</small>
                                    @endif
                                    @if($client->codice_fiscale)
                                        <br><small class="text-muted">C.F.: {{ $client->codice_fiscale }}</small>
                                    @endif
                                </td>
                                <td>
                                    <span class="badge {{ $client->tipo == 'Persona Fisica' ? 'bg-info' : 'bg-success' }}">
                                        {{ $client->tipo }}
                                    </span>
                                </td>
                                <td>
                                    @if($client->email)
                                        <div><i class="fas fa-envelope"></i> {{ $client->email }}</div>
                                    @endif
                                    @if($client->telefono)
                                        <div><i class="fas fa-phone"></i> {{ $client->telefono }}</div>
                                    @endif
                                    @if($client->indirizzo)
                                        <div><i class="fas fa-map-marker-alt"></i> {{ Str::limit($client->indirizzo, 30) }}</div>
                                    @endif
                                </td>
                                <td>
                                    <span class="badge bg-primary">{{ $client->projects_count }} progetti</span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('clients.show', $client) }}" class="btn btn-sm btn-outline-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('clients.edit', $client) }}" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form action="{{ route('clients.destroy', $client) }}" method="POST" class="d-inline">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-sm btn-outline-danger" 
                                                    onclick="return confirm('Sei sicuro di voler eliminare questo cliente?')"
                                                    {{ $client->projects_count > 0 ? 'disabled title=Cliente con progetti associati' : '' }}>
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-center">
                    {{ $clients->links() }}
                </div>
            @else
                <div class="text-center py-4">
                    <i class="fas fa-handshake fa-3x text-muted mb-3"></i>
                    <h5>Nessun cliente trovato</h5>
                    <p class="text-muted">Non ci sono clienti che corrispondono ai criteri di ricerca.</p>
                    <a href="{{ route('clients.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Aggiungi il primo cliente
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
