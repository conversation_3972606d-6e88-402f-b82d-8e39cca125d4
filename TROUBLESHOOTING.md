# Risoluzione Problemi - 36k Management System

## 🎨 Problemi con gli Stili CSS

### Problema: Gli stili non vengono caricati correttamente

**Sintomi:**
- La pagina appare senza stili Bootstrap
- Il layout non è responsive
- I colori e i gradienti non sono visibili
- La sidebar non funziona correttamente

**Soluzioni:**

#### 1. Ricompila gli Asset
```bash
# Ferma tutti i processi in corso (Ctrl+C)
npm run build
```

#### 2. Per Sviluppo - Usa il Server di Sviluppo
```bash
# Terminale 1: Avvia Vite dev server
npm run dev

# Terminale 2: Avvia Laravel server
php artisan serve
```

#### 3. Pulisci la Cache del Browser
- Premi `Ctrl+F5` (Windows) o `Cmd+Shift+R` (Mac)
- Oppure apri gli strumenti sviluppatore (F12) e fai click destro sul pulsante refresh → "Empty Cache and Hard Reload"

#### 4. Verifica i File Asset
Controlla che esistano questi file:
```
public/build/assets/app-[hash].css
public/build/assets/app-[hash].js
public/build/manifest.json
```

Se non esistono, esegui:
```bash
npm install
npm run build
```

#### 5. Problemi con Node.js/NPM
```bash
# Pulisci la cache npm
npm cache clean --force

# Reinstalla le dipendenze
rm -rf node_modules package-lock.json
npm install

# Ricompila
npm run build
```

### Problema: Errori durante la compilazione

**Errore: "Cannot resolve Bootstrap"**
```bash
npm install bootstrap @popperjs/core --save
npm run build
```

**Errore: "Vite not found"**
```bash
npm install --save-dev vite laravel-vite-plugin
npm run build
```

## 🖥️ Problemi con il Server

### Problema: Pagina bianca o errore 500

**Soluzioni:**
```bash
# Verifica i permessi
php artisan storage:link

# Pulisci le cache
php artisan cache:clear
php artisan config:clear
php artisan view:clear

# Verifica il database
php artisan migrate:status
```

### Problema: Errori di autenticazione

**Soluzioni:**
```bash
# Rigenera la chiave dell'applicazione
php artisan key:generate

# Verifica i seeder
php artisan db:seed --class=AdminUserSeeder
```

## 📱 Problemi di Responsive Design

### Problema: Layout non responsive su mobile

**Verifica:**
1. Che il tag viewport sia presente nel layout:
```html
<meta name="viewport" content="width=device-width, initial-scale=1">
```

2. Che Bootstrap CSS sia caricato correttamente
3. Che gli stili custom non sovrascrivano Bootstrap

### Problema: Sidebar non funziona su mobile

**Verifica:**
1. Che Bootstrap JavaScript sia caricato
2. Che gli script personalizzati siano eseguiti dopo Bootstrap

## 🔧 Comandi Utili per il Debug

### Verifica Stato Asset
```bash
# Lista i file compilati
ls -la public/build/assets/

# Verifica il manifest
cat public/build/manifest.json
```

### Verifica Configurazione Laravel
```bash
# Mostra la configurazione
php artisan config:show

# Verifica le route
php artisan route:list

# Verifica i middleware
php artisan route:list --middleware=admin
```

### Test Funzionalità
```bash
# Esegui i test
php artisan test

# Test specifici
php artisan test --filter=EmployeeManagementTest
```

## 🚀 Avvio Rapido per Sviluppo

Per avviare rapidamente l'ambiente di sviluppo:

```bash
# 1. Installa dipendenze (solo la prima volta)
composer install
npm install

# 2. Configura ambiente (solo la prima volta)
cp .env.example .env
php artisan key:generate
php artisan migrate
php artisan db:seed
php artisan storage:link

# 3. Avvia i server (ogni volta)
# Terminale 1:
npm run dev

# Terminale 2:
php artisan serve
```

Poi apri il browser su `http://localhost:8000`

## 📞 Supporto

Se i problemi persistono:
1. Controlla i log di Laravel in `storage/logs/laravel.log`
2. Controlla la console del browser (F12) per errori JavaScript
3. Verifica che tutti i requisiti di sistema siano soddisfatti
4. Prova a ricreare il database: `php artisan migrate:fresh --seed`

## 🔄 Reset Completo

Se nulla funziona, reset completo:

```bash
# Pulisci tutto
rm -rf node_modules package-lock.json
rm -rf public/build
php artisan cache:clear
php artisan config:clear
php artisan view:clear

# Reinstalla tutto
npm install
composer install
npm run build
php artisan migrate:fresh --seed
php artisan storage:link

# Avvia i server
npm run dev  # Terminale 1
php artisan serve  # Terminale 2
```
