<?php

namespace Tests\Feature;

use App\Models\Employee;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class EmployeeCreationTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test that creating an employee also creates a corresponding user.
     */
    public function test_employee_creation_creates_user(): void
    {
        // Create an admin user for authentication
        $admin = User::factory()->create([
            'role' => 'Administrator'
        ]);

        // Employee data
        $employeeData = [
            'nome' => 'Mario',
            'cognome' => '<PERSON>',
            'ruolo' => 'Fotografo',
            'telefono' => '1234567890',
            'email' => '<EMAIL>',
        ];

        // Act as admin and create employee
        $response = $this->actingAs($admin)
            ->post(route('employees.store'), $employeeData);

        // Assert employee was created
        $this->assertDatabaseHas('employees', [
            'nome' => '<PERSON>',
            'cognome' => '<PERSON>',
            'email' => '<EMAIL>',
        ]);

        // Assert corresponding user was created
        $this->assertDatabaseHas('users', [
            'name' => '<PERSON> Rossi',
            'email' => '<EMAIL>',
            'role' => 'Dipendente',
        ]);

        // Assert redirect
        $response->assertRedirect(route('employees.index'));
    }

    /**
     * Test employee creation with photo upload.
     */
    public function test_employee_creation_with_photo(): void
    {
        Storage::fake('public');

        // Create an admin user for authentication
        $admin = User::factory()->create([
            'role' => 'Administrator'
        ]);

        // Create a fake image file
        $file = UploadedFile::fake()->image('employee.jpg');

        // Employee data with photo
        $employeeData = [
            'nome' => 'Luigi',
            'cognome' => 'Verdi',
            'ruolo' => 'Editor Video',
            'telefono' => '0987654321',
            'email' => '<EMAIL>',
            'foto' => $file,
        ];

        // Act as admin and create employee
        $response = $this->actingAs($admin)
            ->post(route('employees.store'), $employeeData);

        // Assert employee was created
        $employee = Employee::where('email', '<EMAIL>')->first();
        $this->assertNotNull($employee);
        $this->assertNotNull($employee->foto);

        // Assert corresponding user was created with photo
        $user = User::where('email', '<EMAIL>')->first();
        $this->assertNotNull($user);
        $this->assertEquals($employee->foto, $user->foto);

        // Assert file was stored
        $this->assertTrue(Storage::disk('public')->exists($employee->foto));

        // Assert redirect
        $response->assertRedirect(route('employees.index'));
    }

    /**
     * Test that email uniqueness is enforced across both tables.
     */
    public function test_email_uniqueness_across_tables(): void
    {
        // Create an admin user for authentication
        $admin = User::factory()->create([
            'role' => 'Administrator'
        ]);

        // Create a user with an email
        User::factory()->create([
            'email' => '<EMAIL>'
        ]);

        // Try to create employee with same email
        $employeeData = [
            'nome' => 'Test',
            'cognome' => 'User',
            'ruolo' => 'Fotografo',
            'telefono' => '1234567890',
            'email' => '<EMAIL>',
        ];

        $response = $this->actingAs($admin)
            ->post(route('employees.store'), $employeeData);

        // Assert validation error
        $response->assertSessionHasErrors('email');

        // Assert employee was not created
        $this->assertDatabaseMissing('employees', [
            'email' => '<EMAIL>'
        ]);
    }

    /**
     * Test employee update also updates corresponding user.
     */
    public function test_employee_update_updates_user(): void
    {
        // Create an admin user for authentication
        $admin = User::factory()->create([
            'role' => 'Administrator'
        ]);

        // Create employee and corresponding user
        $employee = Employee::factory()->create([
            'nome' => 'Original',
            'cognome' => 'Name',
            'email' => '<EMAIL>',
        ]);

        $user = User::factory()->create([
            'name' => 'Original Name',
            'email' => '<EMAIL>',
            'role' => 'Dipendente',
        ]);

        // Update data
        $updateData = [
            'nome' => 'Updated',
            'cognome' => 'Name',
            'ruolo' => 'Fotografo',
            'telefono' => '1234567890',
            'email' => '<EMAIL>',
        ];

        // Update employee
        $response = $this->actingAs($admin)
            ->put(route('employees.update', $employee), $updateData);

        // Assert employee was updated
        $this->assertDatabaseHas('employees', [
            'id' => $employee->id,
            'nome' => 'Updated',
            'email' => '<EMAIL>',
        ]);

        // Assert corresponding user was updated
        $this->assertDatabaseHas('users', [
            'id' => $user->id,
            'name' => 'Updated Name',
            'email' => '<EMAIL>',
        ]);

        // Assert redirect
        $response->assertRedirect(route('employees.index'));
    }

    /**
     * Test employee deletion also deletes corresponding user.
     */
    public function test_employee_deletion_deletes_user(): void
    {
        // Create an admin user for authentication
        $admin = User::factory()->create([
            'role' => 'Administrator'
        ]);

        // Create employee and corresponding user
        $employee = Employee::factory()->create([
            'email' => '<EMAIL>',
        ]);

        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'role' => 'Dipendente',
        ]);

        // Delete employee
        $response = $this->actingAs($admin)
            ->delete(route('employees.destroy', $employee));

        // Assert employee was deleted (soft delete)
        $this->assertSoftDeleted('employees', [
            'id' => $employee->id,
        ]);

        // Assert corresponding user was deleted
        $this->assertDatabaseMissing('users', [
            'id' => $user->id,
        ]);

        // Assert redirect
        $response->assertRedirect(route('employees.index'));
    }
}
