<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Task;

class UpdateOverdueTasks extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'tasks:update-overdue';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update overdue tasks status to "Scaduto"';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Checking for overdue tasks...');

        $overdueTasks = Task::where('data_scadenza', '<', now())
            ->whereNotIn('status', ['Completato', 'Scaduto'])
            ->get();

        if ($overdueTasks->count() === 0) {
            $this->info('No overdue tasks found.');
            return 0;
        }

        $updatedCount = Task::where('data_scadenza', '<', now())
            ->whereNotIn('status', ['Completato', 'Scaduto'])
            ->update(['status' => 'Scaduto']);

        $this->info("Updated {$updatedCount} overdue tasks to 'Scaduto' status.");

        // Display updated tasks
        foreach ($overdueTasks as $task) {
            $this->line("- {$task->titolo} (Project: {$task->project->titolo})");
        }

        return 0;
    }
}
