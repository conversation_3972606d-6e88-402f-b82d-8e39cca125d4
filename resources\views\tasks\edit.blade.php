@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-6">
            <h1 class="h3 mb-0">Modifica Task</h1>
            <p class="text-muted">Modifica le informazioni del task "{{ $task->titolo }}"</p>
        </div>
        <div class="col-md-6 text-end">
            <a href="{{ route('tasks.show', $task) }}" class="btn btn-info">
                <i class="fas fa-eye"></i> Visualizza
            </a>
            <a href="{{ route('tasks.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Torna alla lista
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Modifica Informazioni Task</h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('tasks.update', $task) }}" method="POST">
                        @csrf
                        @method('PUT')
                        
                        @if(auth()->user()->isAdmin())
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="project_id" class="form-label">Progetto <span class="text-danger">*</span></label>
                                    <select class="form-select @error('project_id') is-invalid @enderror" 
                                            id="project_id" name="project_id" required>
                                        <option value="">Seleziona un progetto</option>
                                        @foreach($projects as $project)
                                            <option value="{{ $project->id }}" 
                                                    {{ (old('project_id', $task->project_id) == $project->id) ? 'selected' : '' }}>
                                                {{ $project->titolo }} - {{ $project->client->nome ?? $project->client->ragione_sociale }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('project_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="user_id" class="form-label">Assegna a <span class="text-danger">*</span></label>
                                    <select class="form-select @error('user_id') is-invalid @enderror" 
                                            id="user_id" name="user_id" required>
                                        <option value="">Seleziona un dipendente</option>
                                        @foreach($users as $user)
                                            <option value="{{ $user->id }}" 
                                                    {{ (old('user_id', $task->user_id) == $user->id) ? 'selected' : '' }}>
                                                {{ $user->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('user_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        @else
                        <!-- For employees, show read-only project and assignment info -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Progetto</label>
                                    <p class="form-control-plaintext">{{ $task->project->titolo }}</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Assegnato a</label>
                                    <p class="form-control-plaintext">{{ $task->user->name }}</p>
                                </div>
                            </div>
                        </div>
                        @endif

                        <div class="mb-3">
                            <label for="titolo" class="form-label">Titolo <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('titolo') is-invalid @enderror" 
                                   id="titolo" name="titolo" value="{{ old('titolo', $task->titolo) }}" required>
                            @error('titolo')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="descrizione" class="form-label">Descrizione</label>
                            <textarea class="form-control @error('descrizione') is-invalid @enderror" 
                                      id="descrizione" name="descrizione" rows="4" 
                                      placeholder="Descrizione dettagliata del task...">{{ old('descrizione', $task->descrizione) }}</textarea>
                            @error('descrizione')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                    <select class="form-select @error('status') is-invalid @enderror" 
                                            id="status" name="status" required>
                                        <option value="Assegnato" {{ old('status', $task->status) == 'Assegnato' ? 'selected' : '' }}>
                                            Assegnato
                                        </option>
                                        <option value="In Corso" {{ old('status', $task->status) == 'In Corso' ? 'selected' : '' }}>
                                            In Corso
                                        </option>
                                        <option value="Completato" {{ old('status', $task->status) == 'Completato' ? 'selected' : '' }}>
                                            Completato
                                        </option>
                                    </select>
                                    @error('status')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="data_scadenza" class="form-label">Data Scadenza <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control @error('data_scadenza') is-invalid @enderror" 
                                           id="data_scadenza" name="data_scadenza" 
                                           value="{{ old('data_scadenza', $task->data_scadenza->format('Y-m-d')) }}" required>
                                    @error('data_scadenza')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ route('tasks.show', $task) }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Annulla
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Salva Modifiche
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Informazioni Task</h5>
                </div>
                <div class="card-body">
                    <p><strong>Status Attuale:</strong> 
                        <span class="badge {{ $task->getStatusBadgeClass() }}">{{ $task->status }}</span>
                    </p>
                    <p><strong>Creato il:</strong> {{ $task->created_at->format('d/m/Y H:i') }}</p>
                    @if($task->updated_at != $task->created_at)
                        <p><strong>Ultima modifica:</strong> {{ $task->updated_at->format('d/m/Y H:i') }}</p>
                    @endif
                    @if($task->isOverdue())
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>Attenzione!</strong> Questo task è scaduto.
                        </div>
                    @endif
                </div>
            </div>

            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">Progetto Collegato</h5>
                </div>
                <div class="card-body">
                    <h6>{{ $task->project->titolo }}</h6>
                    <p class="text-muted">{{ $task->project->client->nome ?? $task->project->client->ragione_sociale }}</p>
                    <p><strong>Status:</strong> 
                        <span class="badge badge-{{ strtolower(str_replace(' ', '-', $task->project->stato)) }}">
                            {{ $task->project->stato }}
                        </span>
                    </p>
                    <div class="mt-3">
                        <a href="{{ route('projects.show', $task->project) }}" class="btn btn-outline-primary btn-sm w-100">
                            <i class="fas fa-eye"></i> Vedi Progetto
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.badge.bg-secondary { background-color: #6c757d !important; }
.badge.bg-primary { background-color: #0d6efd !important; }
.badge.bg-success { background-color: #198754 !important; }
.badge.bg-danger { background-color: #dc3545 !important; }
.badge.badge-in-attesa { background-color: #ffc107; color: #000; }
.badge.badge-in-corso { background-color: #0d6efd; }
.badge.badge-completato { background-color: #198754; }
.badge.badge-annullato { background-color: #6c757d; }
</style>
@endsection
