<?php
/**
 * Script di verifica per il setup del progetto 36k Management System
 */

echo "🔍 Verifica Setup 36k Management System\n";
echo "=====================================\n\n";

// Verifica file essenziali
$requiredFiles = [
    'public/build/manifest.json' => 'Manifest Vite',
    'database/database.sqlite' => 'Database SQLite',
    'storage/app/public' => 'Directory Storage',
    'resources/css/app.css' => 'CSS principale',
    'resources/css/custom.css' => 'CSS personalizzato',
    'resources/js/app.js' => 'JavaScript principale',
];

echo "📁 Verifica File:\n";
foreach ($requiredFiles as $file => $description) {
    if (file_exists($file)) {
        echo "✅ $description: OK\n";
    } else {
        echo "❌ $description: MANCANTE ($file)\n";
    }
}

echo "\n";

// Verifica asset compilati
echo "🎨 Verifica Asset Compilati:\n";
$buildDir = 'public/build/assets';
if (is_dir($buildDir)) {
    $files = glob($buildDir . '/*');
    $cssFiles = array_filter($files, fn($f) => str_ends_with($f, '.css'));
    $jsFiles = array_filter($files, fn($f) => str_ends_with($f, '.js'));
    
    echo "✅ CSS compilati: " . count($cssFiles) . " file(s)\n";
    echo "✅ JS compilati: " . count($jsFiles) . " file(s)\n";
    
    if (count($cssFiles) > 0 && count($jsFiles) > 0) {
        echo "✅ Asset compilati correttamente\n";
    } else {
        echo "❌ Asset non compilati - esegui 'npm run build'\n";
    }
} else {
    echo "❌ Directory build non trovata - esegui 'npm run build'\n";
}

echo "\n";

// Verifica database
echo "🗄️ Verifica Database:\n";
try {
    $pdo = new PDO('sqlite:database/database.sqlite');
    
    // Verifica tabelle
    $tables = ['users', 'employees', 'clients', 'projects'];
    foreach ($tables as $table) {
        $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
        $count = $stmt->fetchColumn();
        echo "✅ Tabella $table: $count record(s)\n";
    }
    
    // Verifica utente admin
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE role = 'Administrator'");
    $stmt->execute();
    $adminCount = $stmt->fetchColumn();
    
    if ($adminCount > 0) {
        echo "✅ Utente amministratore presente\n";
    } else {
        echo "❌ Utente amministratore mancante - esegui 'php artisan db:seed'\n";
    }
    
} catch (Exception $e) {
    echo "❌ Errore database: " . $e->getMessage() . "\n";
    echo "💡 Esegui: php artisan migrate && php artisan db:seed\n";
}

echo "\n";

// Verifica permessi
echo "🔐 Verifica Permessi:\n";
$directories = [
    'storage/app' => 'Storage app',
    'storage/framework' => 'Storage framework',
    'storage/logs' => 'Storage logs',
    'bootstrap/cache' => 'Bootstrap cache',
];

foreach ($directories as $dir => $description) {
    if (is_writable($dir)) {
        echo "✅ $description: Scrivibile\n";
    } else {
        echo "❌ $description: Non scrivibile\n";
    }
}

echo "\n";

// Verifica configurazione
echo "⚙️ Verifica Configurazione:\n";
if (file_exists('.env')) {
    echo "✅ File .env presente\n";
    
    $env = file_get_contents('.env');
    if (strpos($env, 'APP_KEY=base64:') !== false) {
        echo "✅ APP_KEY configurata\n";
    } else {
        echo "❌ APP_KEY mancante - esegui 'php artisan key:generate'\n";
    }
} else {
    echo "❌ File .env mancante - copia .env.example\n";
}

echo "\n";

// Riepilogo
echo "📋 RIEPILOGO:\n";
echo "=============\n";
echo "Se tutti i controlli sono ✅, il sistema è pronto!\n\n";
echo "Per avviare il sistema:\n";
echo "1. npm run dev (in un terminale)\n";
echo "2. php artisan serve (in un altro terminale)\n";
echo "3. Apri http://localhost:8000\n\n";
echo "Credenziali di default:\n";
echo "- Admin: <EMAIL> / admin123\n";
echo "- Dipendente: <EMAIL> / dipendente123\n\n";
echo "Per problemi, consulta TROUBLESHOOTING.md\n";
?>
