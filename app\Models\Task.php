<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class Task extends Model
{
    use SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'project_id',
        'user_id',
        'titolo',
        'descrizione',
        'status',
        'data_scadenza',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'data_scadenza' => 'date',
        ];
    }

    /**
     * Get the project that owns the task.
     */
    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    /**
     * Get the user assigned to the task.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Check if the task is overdue.
     */
    public function isOverdue(): bool
    {
        return $this->data_scadenza < now() && $this->status !== 'Completato';
    }

    /**
     * Get the status badge class for styling.
     */
    public function getStatusBadgeClass(): string
    {
        return match($this->status) {
            'Assegnato' => 'bg-secondary',
            'In Corso' => 'bg-primary',
            'Completato' => 'bg-success',
            'Scaduto' => 'bg-danger',
            default => 'bg-secondary'
        };
    }

    /**
     * Scope to get overdue tasks.
     */
    public function scopeOverdue($query)
    {
        return $query->where('data_scadenza', '<', now())
                    ->whereNotIn('status', ['Completato']);
    }

    /**
     * Scope to get tasks by status.
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get tasks assigned to a specific user.
     */
    public function scopeAssignedTo($query, $userId)
    {
        return $query->where('user_id', $userId);
    }
}
