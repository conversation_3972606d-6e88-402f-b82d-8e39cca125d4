<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tasks', function (Blueprint $table) {
            $table->id();
            $table->foreignId('project_id')->constrained()->onDelete('cascade'); // Link to project
            $table->foreignId('user_id')->constrained()->onDelete('cascade'); // Link to assigned employee
            $table->string('titolo'); // Task title
            $table->text('descrizione')->nullable(); // Task description
            $table->enum('status', ['Assegnato', 'In Corso', 'Completato', 'Scaduto'])->default('Assegnato'); // Task status
            $table->date('data_scadenza'); // Due date
            $table->timestamps();
            $table->softDeletes(); // For soft delete functionality
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tasks');
    }
};
