<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Client extends Model
{
    use SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'nome',
        'cognome',
        'tipo',
        'email',
        'telefono',
        'indirizzo',
        'partita_iva',
        'codice_fiscale',
        'note',
    ];

    /**
     * Get the client's full name or company name.
     */
    public function getFullNameAttribute(): string
    {
        if ($this->tipo === 'Persona Giuridica') {
            return $this->nome; // Company name
        }

        return $this->nome . ($this->cognome ? ' ' . $this->cognome : '');
    }

    /**
     * Get all projects for this client.
     */
    public function projects(): HasMany
    {
        return $this->hasMany(Project::class);
    }
}
