<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create default administrator user
        User::create([
            'name' => 'Administrator',
            'email' => '<EMAIL>',
            'password' => Hash::make('admin123'),
            'role' => 'Administrator',
            'email_verified_at' => now(),
        ]);

        // Create a sample employee user
        User::create([
            'name' => '<PERSON>',
            'email' => '<EMAIL>',
            'password' => Hash::make('dipendente123'),
            'role' => 'Dipendente',
            'email_verified_at' => now(),
        ]);
    }
}
