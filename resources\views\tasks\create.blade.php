@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-6">
            <h1 class="h3 mb-0">Nuovo Task</h1>
            <p class="text-muted">Crea un nuovo task da assegnare a un dipendente</p>
        </div>
        <div class="col-md-6 text-end">
            <a href="{{ route('tasks.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Torna alla lista
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Informazioni Task</h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('tasks.store') }}" method="POST">
                        @csrf
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="project_id" class="form-label">Progetto <span class="text-danger">*</span></label>
                                    <select class="form-select @error('project_id') is-invalid @enderror" 
                                            id="project_id" name="project_id" required>
                                        <option value="">Seleziona un progetto</option>
                                        @foreach($projects as $project)
                                            <option value="{{ $project->id }}" 
                                                    {{ (old('project_id', $selectedProject?->id) == $project->id) ? 'selected' : '' }}>
                                                {{ $project->titolo }} - {{ $project->client->nome ?? $project->client->ragione_sociale }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('project_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="user_id" class="form-label">Assegna a <span class="text-danger">*</span></label>
                                    <select class="form-select @error('user_id') is-invalid @enderror" 
                                            id="user_id" name="user_id" required>
                                        <option value="">Seleziona un dipendente</option>
                                        @foreach($users as $user)
                                            <option value="{{ $user->id }}" {{ old('user_id') == $user->id ? 'selected' : '' }}>
                                                {{ $user->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('user_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="titolo" class="form-label">Titolo <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('titolo') is-invalid @enderror" 
                                   id="titolo" name="titolo" value="{{ old('titolo') }}" required>
                            @error('titolo')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="descrizione" class="form-label">Descrizione</label>
                            <textarea class="form-control @error('descrizione') is-invalid @enderror" 
                                      id="descrizione" name="descrizione" rows="4" 
                                      placeholder="Descrizione dettagliata del task...">{{ old('descrizione') }}</textarea>
                            @error('descrizione')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                    <select class="form-select @error('status') is-invalid @enderror" 
                                            id="status" name="status" required>
                                        <option value="Assegnato" {{ old('status', 'Assegnato') == 'Assegnato' ? 'selected' : '' }}>
                                            Assegnato
                                        </option>
                                        <option value="In Corso" {{ old('status') == 'In Corso' ? 'selected' : '' }}>
                                            In Corso
                                        </option>
                                        <option value="Completato" {{ old('status') == 'Completato' ? 'selected' : '' }}>
                                            Completato
                                        </option>
                                    </select>
                                    @error('status')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="data_scadenza" class="form-label">Data Scadenza <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control @error('data_scadenza') is-invalid @enderror" 
                                           id="data_scadenza" name="data_scadenza" 
                                           value="{{ old('data_scadenza') }}" 
                                           min="{{ date('Y-m-d', strtotime('+1 day')) }}" required>
                                    @error('data_scadenza')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ route('tasks.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Annulla
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Crea Task
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Informazioni</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <strong>Suggerimenti:</strong>
                        <ul class="mb-0 mt-2">
                            <li>Scegli un titolo descrittivo per il task</li>
                            <li>Aggiungi una descrizione dettagliata per chiarire gli obiettivi</li>
                            <li>Imposta una data di scadenza realistica</li>
                            <li>Il dipendente riceverà una notifica del nuovo task</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
