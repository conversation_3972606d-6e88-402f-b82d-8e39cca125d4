/* Custom styles for 36k Management System */

/* Base styles */
body {
    font-family: 'Figtree', sans-serif;
    background-color: #f8f9fa;
}

/* Ensure Bootstrap is working */
.container-fluid {
    padding-left: 15px;
    padding-right: 15px;
}

.btn {
    border-radius: 6px;
    font-weight: 500;
}

.card {
    border: none;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-radius: 10px;
}

/* Sidebar styles */
.sidebar {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    width: 250px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    transition: all 0.3s ease;
    z-index: 1000;
    overflow-y: auto;
}

.sidebar.collapsed {
    width: 70px;
}

.sidebar .sidebar-header {
    padding: 20px;
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar .sidebar-header h3 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: bold;
}

.sidebar.collapsed .sidebar-header h3 {
    display: none;
}

.sidebar .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 15px 20px;
    border-radius: 0;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
}

.sidebar .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
}

.sidebar .nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
}

.sidebar .nav-link i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

.sidebar.collapsed .nav-link span {
    display: none;
}

/* Main content area */
.main-content {
    margin-left: 250px;
    transition: all 0.3s ease;
    min-height: 100vh;
}

.main-content.expanded {
    margin-left: 70px;
}

/* Top navigation bar */
.top-navbar {
    background: white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 10px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.top-navbar .navbar-toggle {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: #666;
}

.top-navbar .user-info {
    display: flex;
    align-items: center;
}

.top-navbar .user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 10px;
    object-fit: cover;
}

.top-navbar .user-name {
    margin-right: 10px;
    font-weight: 500;
}

/* Content area */
.content-area {
    padding: 20px;
}

/* Card styles */

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px 10px 0 0 !important;
    border: none;
}

/* Button styles */
.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-1px);
}

/* Table styles */
.table {
    border-radius: 10px;
    overflow: hidden;
}

.table thead th {
    background-color: #f8f9fa;
    border: none;
    font-weight: 600;
}

/* Form styles */
.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* Responsive design */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .main-content.expanded {
        margin-left: 0;
    }
}

/* Profile photo placeholder */
.profile-photo-placeholder {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
}

/* Status badges */
.badge-in-attesa {
    background-color: #ffc107;
    color: #000;
}

.badge-in-corso {
    background-color: #007bff;
}

.badge-completato {
    background-color: #28a745;
}

.badge-annullato {
    background-color: #dc3545;
}

/* Animation for smooth transitions */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}
