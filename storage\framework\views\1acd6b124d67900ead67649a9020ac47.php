<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-6">
            <h1 class="h3 mb-0">Modifica Task</h1>
            <p class="text-muted">Modifica le informazioni del task "<?php echo e($task->titolo); ?>"</p>
        </div>
        <div class="col-md-6 text-end">
            <a href="<?php echo e(route('tasks.show', $task)); ?>" class="btn btn-info">
                <i class="fas fa-eye"></i> Visualizza
            </a>
            <a href="<?php echo e(route('tasks.index')); ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Torna alla lista
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Modifica Informazioni Task</h5>
                </div>
                <div class="card-body">
                    <form action="<?php echo e(route('tasks.update', $task)); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('PUT'); ?>
                        
                        <?php if(auth()->user()->isAdmin()): ?>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="project_id" class="form-label">Progetto <span class="text-danger">*</span></label>
                                    <select class="form-select <?php $__errorArgs = ['project_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                            id="project_id" name="project_id" required>
                                        <option value="">Seleziona un progetto</option>
                                        <?php $__currentLoopData = $projects; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $project): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($project->id); ?>" 
                                                    <?php echo e((old('project_id', $task->project_id) == $project->id) ? 'selected' : ''); ?>>
                                                <?php echo e($project->titolo); ?> - <?php echo e($project->client->nome ?? $project->client->ragione_sociale); ?>

                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                    <?php $__errorArgs = ['project_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="user_id" class="form-label">Assegna a <span class="text-danger">*</span></label>
                                    <select class="form-select <?php $__errorArgs = ['user_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                            id="user_id" name="user_id" required>
                                        <option value="">Seleziona un dipendente</option>
                                        <?php $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($user->id); ?>" 
                                                    <?php echo e((old('user_id', $task->user_id) == $user->id) ? 'selected' : ''); ?>>
                                                <?php echo e($user->name); ?>

                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                    <?php $__errorArgs = ['user_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                        </div>
                        <?php else: ?>
                        <!-- For employees, show read-only project and assignment info -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Progetto</label>
                                    <p class="form-control-plaintext"><?php echo e($task->project->titolo); ?></p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Assegnato a</label>
                                    <p class="form-control-plaintext"><?php echo e($task->user->name); ?></p>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>

                        <div class="mb-3">
                            <label for="titolo" class="form-label">Titolo <span class="text-danger">*</span></label>
                            <input type="text" class="form-control <?php $__errorArgs = ['titolo'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                   id="titolo" name="titolo" value="<?php echo e(old('titolo', $task->titolo)); ?>" required>
                            <?php $__errorArgs = ['titolo'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="mb-3">
                            <label for="descrizione" class="form-label">Descrizione</label>
                            <textarea class="form-control <?php $__errorArgs = ['descrizione'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                      id="descrizione" name="descrizione" rows="4" 
                                      placeholder="Descrizione dettagliata del task..."><?php echo e(old('descrizione', $task->descrizione)); ?></textarea>
                            <?php $__errorArgs = ['descrizione'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                    <select class="form-select <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                            id="status" name="status" required>
                                        <option value="Assegnato" <?php echo e(old('status', $task->status) == 'Assegnato' ? 'selected' : ''); ?>>
                                            Assegnato
                                        </option>
                                        <option value="In Corso" <?php echo e(old('status', $task->status) == 'In Corso' ? 'selected' : ''); ?>>
                                            In Corso
                                        </option>
                                        <option value="Completato" <?php echo e(old('status', $task->status) == 'Completato' ? 'selected' : ''); ?>>
                                            Completato
                                        </option>
                                    </select>
                                    <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="data_scadenza" class="form-label">Data Scadenza <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control <?php $__errorArgs = ['data_scadenza'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                           id="data_scadenza" name="data_scadenza" 
                                           value="<?php echo e(old('data_scadenza', $task->data_scadenza->format('Y-m-d'))); ?>" required>
                                    <?php $__errorArgs = ['data_scadenza'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="<?php echo e(route('tasks.show', $task)); ?>" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Annulla
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Salva Modifiche
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Informazioni Task</h5>
                </div>
                <div class="card-body">
                    <p><strong>Status Attuale:</strong> 
                        <span class="badge <?php echo e($task->getStatusBadgeClass()); ?>"><?php echo e($task->status); ?></span>
                    </p>
                    <p><strong>Creato il:</strong> <?php echo e($task->created_at->format('d/m/Y H:i')); ?></p>
                    <?php if($task->updated_at != $task->created_at): ?>
                        <p><strong>Ultima modifica:</strong> <?php echo e($task->updated_at->format('d/m/Y H:i')); ?></p>
                    <?php endif; ?>
                    <?php if($task->isOverdue()): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>Attenzione!</strong> Questo task è scaduto.
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">Progetto Collegato</h5>
                </div>
                <div class="card-body">
                    <h6><?php echo e($task->project->titolo); ?></h6>
                    <p class="text-muted"><?php echo e($task->project->client->nome ?? $task->project->client->ragione_sociale); ?></p>
                    <p><strong>Status:</strong> 
                        <span class="badge badge-<?php echo e(strtolower(str_replace(' ', '-', $task->project->stato))); ?>">
                            <?php echo e($task->project->stato); ?>

                        </span>
                    </p>
                    <div class="mt-3">
                        <a href="<?php echo e(route('projects.show', $task->project)); ?>" class="btn btn-outline-primary btn-sm w-100">
                            <i class="fas fa-eye"></i> Vedi Progetto
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.badge.bg-secondary { background-color: #6c757d !important; }
.badge.bg-primary { background-color: #0d6efd !important; }
.badge.bg-success { background-color: #198754 !important; }
.badge.bg-danger { background-color: #dc3545 !important; }
.badge.badge-in-attesa { background-color: #ffc107; color: #000; }
.badge.badge-in-corso { background-color: #0d6efd; }
.badge.badge-completato { background-color: #198754; }
.badge.badge-annullato { background-color: #6c757d; }
</style>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Lavori\36k\gestionale\laravel\resources\views/tasks/edit.blade.php ENDPATH**/ ?>