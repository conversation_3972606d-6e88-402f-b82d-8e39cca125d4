@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-6">
            <h1 class="h3 mb-0">Gestione Lavori</h1>
            <p class="text-muted">Gestisci i progetti dell'agenzia</p>
        </div>
        <div class="col-md-6 text-end">
            <a href="{{ route('projects.create') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Nuovo Progetto
            </a>
        </div>
    </div>

    <!-- Search and Filter -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="{{ route('projects.index') }}">
                <div class="row">
                    <div class="col-md-3">
                        <input type="text" name="search" class="form-control" placeholder="Cerca per titolo, descrizione..." value="{{ request('search') }}">
                    </div>
                    <div class="col-md-2">
                        <select name="client_id" class="form-select">
                            <option value="">Tutti i clienti</option>
                            @foreach($clients as $client)
                                <option value="{{ $client->id }}" {{ request('client_id') == $client->id ? 'selected' : '' }}>
                                    {{ $client->full_name }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select name="tipo_progetto" class="form-select">
                            <option value="">Tutti i tipi</option>
                            <option value="One-shot" {{ request('tipo_progetto') == 'One-shot' ? 'selected' : '' }}>One-shot</option>
                            <option value="Ricorrente" {{ request('tipo_progetto') == 'Ricorrente' ? 'selected' : '' }}>Ricorrente</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select name="stato" class="form-select">
                            <option value="">Tutti gli stati</option>
                            <option value="In Attesa" {{ request('stato') == 'In Attesa' ? 'selected' : '' }}>In Attesa</option>
                            <option value="In Corso" {{ request('stato') == 'In Corso' ? 'selected' : '' }}>In Corso</option>
                            <option value="Completato" {{ request('stato') == 'Completato' ? 'selected' : '' }}>Completato</option>
                            <option value="Annullato" {{ request('stato') == 'Annullato' ? 'selected' : '' }}>Annullato</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <button type="submit" class="btn btn-outline-primary">
                            <i class="fas fa-search"></i> Cerca
                        </button>
                        <a href="{{ route('projects.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i> Reset
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Projects Table -->
    <div class="card">
        <div class="card-body">
            @if($projects->count() > 0)
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Titolo</th>
                                <th>Cliente</th>
                                <th>Tipo</th>
                                <th>Scadenza/Frequenza</th>
                                <th>Compenso</th>
                                <th>Stato</th>
                                <th>Azioni</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($projects as $project)
                            <tr>
                                <td>
                                    <strong>{{ $project->titolo }}</strong>
                                    <br><small class="text-muted">{{ Str::limit($project->tipologia_lavoro, 50) }}</small>
                                </td>
                                <td>{{ $project->client->full_name }}</td>
                                <td>
                                    <span class="badge {{ $project->tipo_progetto == 'One-shot' ? 'bg-warning' : 'bg-info' }}">
                                        {{ $project->tipo_progetto }}
                                    </span>
                                </td>
                                <td>
                                    @if($project->isOneShot())
                                        @if($project->data_scadenza)
                                            <i class="fas fa-calendar-alt"></i> {{ $project->data_scadenza->format('d/m/Y') }}
                                        @else
                                            <span class="text-muted">Non specificata</span>
                                        @endif
                                    @else
                                        <i class="fas fa-sync-alt"></i> {{ $project->frequenza ?? 'Non specificata' }}
                                    @endif
                                </td>
                                <td>
                                    <strong>€ {{ number_format($project->compenso, 2) }}</strong>
                                </td>
                                <td>
                                    <span class="badge badge-{{ strtolower(str_replace(' ', '-', $project->stato)) }}">
                                        {{ $project->stato }}
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('projects.show', $project) }}" class="btn btn-sm btn-outline-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('projects.edit', $project) }}" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form action="{{ route('projects.destroy', $project) }}" method="POST" class="d-inline">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-sm btn-outline-danger" onclick="return confirm('Sei sicuro di voler eliminare questo progetto?')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-center">
                    {{ $projects->links() }}
                </div>
            @else
                <div class="text-center py-4">
                    <i class="fas fa-project-diagram fa-3x text-muted mb-3"></i>
                    <h5>Nessun progetto trovato</h5>
                    <p class="text-muted">Non ci sono progetti che corrispondono ai criteri di ricerca.</p>
                    <a href="{{ route('projects.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Aggiungi il primo progetto
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
