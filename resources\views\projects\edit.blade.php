@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-6">
            <h1 class="h3 mb-0">Modifica Progetto</h1>
            <p class="text-muted">Modifica le informazioni di "{{ $project->titolo }}"</p>
        </div>
        <div class="col-md-6 text-end">
            <a href="{{ route('projects.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Torna alla lista
            </a>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Informazioni Progetto</h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('projects.update', $project) }}" method="POST">
                        @csrf
                        @method('PUT')
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="client_id" class="form-label">Cliente *</label>
                                    <select class="form-select @error('client_id') is-invalid @enderror" id="client_id" name="client_id" required>
                                        <option value="">Seleziona un cliente</option>
                                        @foreach($clients as $client)
                                            <option value="{{ $client->id }}" 
                                                {{ old('client_id', $project->client_id) == $client->id ? 'selected' : '' }}>
                                                {{ $client->full_name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('client_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="titolo" class="form-label">Titolo Progetto *</label>
                                    <input type="text" class="form-control @error('titolo') is-invalid @enderror" 
                                           id="titolo" name="titolo" value="{{ old('titolo', $project->titolo) }}" required>
                                    @error('titolo')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="tipologia_lavoro" class="form-label">Tipologia di Lavoro *</label>
                            <textarea class="form-control @error('tipologia_lavoro') is-invalid @enderror" 
                                      id="tipologia_lavoro" name="tipologia_lavoro" rows="3" required>{{ old('tipologia_lavoro', $project->tipologia_lavoro) }}</textarea>
                            <div class="form-text">Descrivi il tipo di lavoro da svolgere</div>
                            @error('tipologia_lavoro')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="tipo_progetto" class="form-label">Tipo Progetto *</label>
                                    <select class="form-select @error('tipo_progetto') is-invalid @enderror" id="tipo_progetto" name="tipo_progetto" required>
                                        <option value="">Seleziona tipo</option>
                                        <option value="One-shot" {{ old('tipo_progetto', $project->tipo_progetto) == 'One-shot' ? 'selected' : '' }}>One-shot</option>
                                        <option value="Ricorrente" {{ old('tipo_progetto', $project->tipo_progetto) == 'Ricorrente' ? 'selected' : '' }}>Ricorrente</option>
                                    </select>
                                    @error('tipo_progetto')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-4" id="data_scadenza_field">
                                <div class="mb-3">
                                    <label for="data_scadenza" class="form-label">Data Scadenza</label>
                                    <input type="date" class="form-control @error('data_scadenza') is-invalid @enderror" 
                                           id="data_scadenza" name="data_scadenza" 
                                           value="{{ old('data_scadenza', $project->data_scadenza?->format('Y-m-d')) }}">
                                    @error('data_scadenza')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-4" id="frequenza_field">
                                <div class="mb-3">
                                    <label for="frequenza" class="form-label">Frequenza</label>
                                    <input type="text" class="form-control @error('frequenza') is-invalid @enderror" 
                                           id="frequenza" name="frequenza" value="{{ old('frequenza', $project->frequenza) }}" 
                                           placeholder="es. Settimanale, Mensile">
                                    @error('frequenza')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="compenso" class="form-label">Compenso (€) *</label>
                                    <input type="number" step="0.01" min="0" class="form-control @error('compenso') is-invalid @enderror" 
                                           id="compenso" name="compenso" value="{{ old('compenso', $project->compenso) }}" required>
                                    @error('compenso')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="stato" class="form-label">Stato *</label>
                                    <select class="form-select @error('stato') is-invalid @enderror" id="stato" name="stato" required>
                                        <option value="In Attesa" {{ old('stato', $project->stato) == 'In Attesa' ? 'selected' : '' }}>In Attesa</option>
                                        <option value="In Corso" {{ old('stato', $project->stato) == 'In Corso' ? 'selected' : '' }}>In Corso</option>
                                        <option value="Completato" {{ old('stato', $project->stato) == 'Completato' ? 'selected' : '' }}>Completato</option>
                                        <option value="Annullato" {{ old('stato', $project->stato) == 'Annullato' ? 'selected' : '' }}>Annullato</option>
                                    </select>
                                    @error('stato')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="descrizione" class="form-label">Descrizione</label>
                            <textarea class="form-control @error('descrizione') is-invalid @enderror" 
                                      id="descrizione" name="descrizione" rows="4">{{ old('descrizione', $project->descrizione) }}</textarea>
                            <div class="form-text">Descrizione dettagliata del progetto</div>
                            @error('descrizione')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="note" class="form-label">Note Interne</label>
                            <textarea class="form-control @error('note') is-invalid @enderror" 
                                      id="note" name="note" rows="3">{{ old('note', $project->note) }}</textarea>
                            <div class="form-text">Note interne per il team</div>
                            @error('note')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ route('projects.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Annulla
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Aggiorna Progetto
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Toggle fields based on project type
document.getElementById('tipo_progetto').addEventListener('change', function() {
    const dataScadenzaField = document.getElementById('data_scadenza_field');
    const frequenzaField = document.getElementById('frequenza_field');
    const dataScadenzaInput = document.getElementById('data_scadenza');
    const frequenzaInput = document.getElementById('frequenza');
    
    if (this.value === 'One-shot') {
        dataScadenzaField.style.display = 'block';
        frequenzaField.style.display = 'none';
        dataScadenzaInput.setAttribute('required', 'required');
        frequenzaInput.removeAttribute('required');
    } else if (this.value === 'Ricorrente') {
        dataScadenzaField.style.display = 'none';
        frequenzaField.style.display = 'block';
        dataScadenzaInput.removeAttribute('required');
        frequenzaInput.setAttribute('required', 'required');
    } else {
        dataScadenzaField.style.display = 'block';
        frequenzaField.style.display = 'none';
        dataScadenzaInput.removeAttribute('required');
        frequenzaInput.removeAttribute('required');
    }
});

// Trigger on page load
document.getElementById('tipo_progetto').dispatchEvent(new Event('change'));
</script>
@endsection
