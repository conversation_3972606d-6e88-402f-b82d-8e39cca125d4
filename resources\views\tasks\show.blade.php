@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-6">
            <h1 class="h3 mb-0">Dettagli Task</h1>
            <p class="text-muted">Informazioni complete di "{{ $task->titolo }}"</p>
        </div>
        <div class="col-md-6 text-end">
            <a href="{{ route('tasks.edit', $task) }}" class="btn btn-primary">
                <i class="fas fa-edit"></i> Modifica
            </a>
            <a href="{{ route('tasks.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Torna alla lista
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Informazioni Task</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Titolo</label>
                                <p class="fw-bold">{{ $task->titolo }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Status</label>
                                <p>
                                    <span class="badge {{ $task->getStatusBadgeClass() }} fs-6">
                                        {{ $task->status }}
                                    </span>
                                    @if($task->isOverdue())
                                        <br><small class="text-danger">
                                            <i class="fas fa-exclamation-triangle"></i> Task scaduto
                                        </small>
                                    @endif
                                </p>
                            </div>
                        </div>
                    </div>

                    @if($task->descrizione)
                    <div class="mb-3">
                        <label class="form-label text-muted">Descrizione</label>
                        <p>{{ $task->descrizione }}</p>
                    </div>
                    @endif

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Progetto Collegato</label>
                                <p>
                                    <a href="{{ route('projects.show', $task->project) }}" class="text-decoration-none">
                                        <i class="fas fa-project-diagram"></i> {{ $task->project->titolo }}
                                    </a>
                                </p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Assegnato a</label>
                                <p class="fw-bold">
                                    <i class="fas fa-user"></i> {{ $task->user->name }}
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Data Scadenza</label>
                                <p class="fw-bold {{ $task->isOverdue() ? 'text-danger' : '' }}">
                                    <i class="fas fa-calendar-alt"></i> {{ $task->data_scadenza->format('d/m/Y') }}
                                    <br><small class="text-muted">
                                        {{ $task->data_scadenza->diffForHumans() }}
                                    </small>
                                </p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Creato il</label>
                                <p>
                                    <i class="fas fa-clock"></i> {{ $task->created_at->format('d/m/Y H:i') }}
                                    <br><small class="text-muted">
                                        {{ $task->created_at->diffForHumans() }}
                                    </small>
                                </p>
                            </div>
                        </div>
                    </div>

                    @if($task->updated_at != $task->created_at)
                    <div class="mb-3">
                        <label class="form-label text-muted">Ultima Modifica</label>
                        <p>
                            <i class="fas fa-edit"></i> {{ $task->updated_at->format('d/m/Y H:i') }}
                            <br><small class="text-muted">
                                {{ $task->updated_at->diffForHumans() }}
                            </small>
                        </p>
                    </div>
                    @endif
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- Project Info Card -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Progetto: {{ $task->project->titolo }}</h5>
                </div>
                <div class="card-body">
                    <p class="mb-2">
                        <strong>Cliente:</strong> 
                        <a href="{{ route('clients.show', $task->project->client) }}" class="text-decoration-none">
                            {{ $task->project->client->nome ?? $task->project->client->ragione_sociale }}
                        </a>
                    </p>
                    <p class="mb-2">
                        <strong>Tipo:</strong> {{ $task->project->tipo_progetto }}
                    </p>
                    <p class="mb-2">
                        <strong>Status Progetto:</strong> 
                        <span class="badge badge-{{ strtolower(str_replace(' ', '-', $task->project->stato)) }}">
                            {{ $task->project->stato }}
                        </span>
                    </p>
                    @if($task->project->data_scadenza)
                    <p class="mb-2">
                        <strong>Scadenza Progetto:</strong> {{ $task->project->data_scadenza->format('d/m/Y') }}
                    </p>
                    @endif
                    <div class="mt-3">
                        <a href="{{ route('projects.show', $task->project) }}" class="btn btn-outline-primary btn-sm w-100">
                            <i class="fas fa-eye"></i> Vedi Progetto
                        </a>
                    </div>
                </div>
            </div>

            <!-- Actions Card -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">Azioni</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('tasks.edit', $task) }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-edit"></i> Modifica Task
                        </a>
                        @if(auth()->user()->isAdmin())
                            <form action="{{ route('tasks.destroy', $task) }}" method="POST">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-danger btn-sm w-100" 
                                        onclick="return confirm('Sei sicuro di voler eliminare questo task?')">
                                    <i class="fas fa-trash"></i> Elimina Task
                                </button>
                            </form>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.badge.bg-secondary { background-color: #6c757d !important; }
.badge.bg-primary { background-color: #0d6efd !important; }
.badge.bg-success { background-color: #198754 !important; }
.badge.bg-danger { background-color: #dc3545 !important; }
.badge.badge-in-attesa { background-color: #ffc107; color: #000; }
.badge.badge-in-corso { background-color: #0d6efd; }
.badge.badge-completato { background-color: #198754; }
.badge.badge-annullato { background-color: #6c757d; }
</style>
@endsection
