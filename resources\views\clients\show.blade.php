@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-6">
            <h1 class="h3 mb-0">Dettagli Cliente</h1>
            <p class="text-muted">Informazioni complete di {{ $client->full_name }}</p>
        </div>
        <div class="col-md-6 text-end">
            <a href="{{ route('clients.edit', $client) }}" class="btn btn-primary">
                <i class="fas fa-edit"></i> Modifica
            </a>
            <a href="{{ route('clients.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Torna alla lista
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Informazioni Cliente</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Nome/Ragione Sociale</label>
                                <p class="fw-bold">{{ $client->nome }}</p>
                            </div>
                        </div>
                        @if($client->cognome)
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Cognome</label>
                                <p class="fw-bold">{{ $client->cognome }}</p>
                            </div>
                        </div>
                        @endif
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Tipo Cliente</label>
                                <p>
                                    <span class="badge {{ $client->tipo == 'Persona Fisica' ? 'bg-info' : 'bg-success' }} fs-6">
                                        {{ $client->tipo }}
                                    </span>
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        @if($client->email)
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Email</label>
                                <p class="fw-bold">
                                    <a href="mailto:{{ $client->email }}" class="text-decoration-none">
                                        <i class="fas fa-envelope"></i> {{ $client->email }}
                                    </a>
                                </p>
                            </div>
                        </div>
                        @endif
                        @if($client->telefono)
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Telefono</label>
                                <p class="fw-bold">
                                    <a href="tel:{{ $client->telefono }}" class="text-decoration-none">
                                        <i class="fas fa-phone"></i> {{ $client->telefono }}
                                    </a>
                                </p>
                            </div>
                        </div>
                        @endif
                    </div>
                    
                    @if($client->indirizzo)
                    <div class="row">
                        <div class="col-12">
                            <div class="mb-3">
                                <label class="form-label text-muted">Indirizzo</label>
                                <p class="fw-bold">{{ $client->indirizzo }}</p>
                            </div>
                        </div>
                    </div>
                    @endif
                    
                    <div class="row">
                        @if($client->partita_iva)
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Partita IVA</label>
                                <p class="fw-bold">{{ $client->partita_iva }}</p>
                            </div>
                        </div>
                        @endif
                        @if($client->codice_fiscale)
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Codice Fiscale</label>
                                <p class="fw-bold">{{ $client->codice_fiscale }}</p>
                            </div>
                        </div>
                        @endif
                    </div>
                    
                    @if($client->note)
                    <div class="row">
                        <div class="col-12">
                            <div class="mb-3">
                                <label class="form-label text-muted">Note</label>
                                <p class="fw-bold">{{ $client->note }}</p>
                            </div>
                        </div>
                    </div>
                    @endif
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Data Creazione</label>
                                <p class="fw-bold">{{ $client->created_at->format('d/m/Y H:i') }}</p>
                            </div>
                        </div>
                        @if($client->updated_at != $client->created_at)
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Ultimo Aggiornamento</label>
                                <p class="fw-bold">{{ $client->updated_at->format('d/m/Y H:i') }}</p>
                            </div>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Progetti Associati</h5>
                </div>
                <div class="card-body">
                    @if($client->projects->count() > 0)
                        <div class="list-group list-group-flush">
                            @foreach($client->projects as $project)
                            <div class="list-group-item px-0">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="mb-1">{{ $project->titolo }}</h6>
                                        <p class="mb-1 text-muted small">{{ Str::limit($project->tipologia_lavoro, 50) }}</p>
                                        <small class="text-muted">€ {{ number_format($project->compenso, 2) }}</small>
                                    </div>
                                    <span class="badge badge-{{ strtolower(str_replace(' ', '-', $project->stato)) }}">
                                        {{ $project->stato }}
                                    </span>
                                </div>
                            </div>
                            @endforeach
                        </div>
                        <div class="mt-3">
                            <a href="{{ route('projects.index', ['client_id' => $client->id]) }}" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-eye"></i> Vedi tutti i progetti
                            </a>
                        </div>
                    @else
                        <p class="text-muted">Nessun progetto associato a questo cliente.</p>
                        <a href="{{ route('projects.create', ['client_id' => $client->id]) }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> Crea primo progetto
                        </a>
                    @endif
                </div>
            </div>
            
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">Azioni</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('projects.create', ['client_id' => $client->id]) }}" class="btn btn-success btn-sm">
                            <i class="fas fa-plus"></i> Nuovo Progetto
                        </a>
                        <form action="{{ route('clients.destroy', $client) }}" method="POST">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger btn-sm w-100" 
                                    onclick="return confirm('Sei sicuro di voler eliminare questo cliente?')"
                                    {{ $client->projects->count() > 0 ? 'disabled' : '' }}>
                                <i class="fas fa-trash"></i> Elimina Cliente
                            </button>
                        </form>
                        @if($client->projects->count() > 0)
                        <small class="text-muted">Non è possibile eliminare un cliente con progetti associati.</small>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
