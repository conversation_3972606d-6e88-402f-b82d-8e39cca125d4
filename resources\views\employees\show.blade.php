@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-6">
            <h1 class="h3 mb-0">Dettagli Dipendente</h1>
            <p class="text-muted">Informazioni complete di {{ $employee->full_name }}</p>
        </div>
        <div class="col-md-6 text-end">
            <a href="{{ route('employees.edit', $employee) }}" class="btn btn-primary">
                <i class="fas fa-edit"></i> Modifica
            </a>
            <a href="{{ route('employees.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Torna alla lista
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-4">
            <div class="card">
                <div class="card-body text-center">
                    @if($employee->foto)
                        <img src="{{ asset('storage/' . $employee->foto) }}" alt="Foto" class="rounded-circle mb-3" style="width: 150px; height: 150px; object-fit: cover;">
                    @else
                        <div class="profile-photo-placeholder mx-auto mb-3" style="width: 150px; height: 150px; font-size: 3rem;">
                            {{ strtoupper(substr($employee->nome, 0, 1)) }}
                        </div>
                    @endif
                    <h4>{{ $employee->full_name }}</h4>
                    <p class="text-muted">{{ $employee->ruolo }}</p>
                    
                    <div class="d-grid gap-2">
                        <form action="{{ route('employees.generate-password', $employee) }}" method="POST">
                            @csrf
                            <button type="submit" class="btn btn-warning btn-sm w-100" onclick="return confirm('Generare una nuova password per questo dipendente?')">
                                <i class="fas fa-key"></i> Genera Nuova Password
                            </button>
                        </form>
                        
                        <form action="{{ route('employees.destroy', $employee) }}" method="POST">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger btn-sm w-100" onclick="return confirm('Sei sicuro di voler eliminare questo dipendente?')">
                                <i class="fas fa-trash"></i> Elimina Dipendente
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Informazioni Personali</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Nome</label>
                                <p class="fw-bold">{{ $employee->nome }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Cognome</label>
                                <p class="fw-bold">{{ $employee->cognome }}</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Ruolo</label>
                                <p><span class="badge bg-info fs-6">{{ $employee->ruolo }}</span></p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Email</label>
                                <p class="fw-bold">
                                    <a href="mailto:{{ $employee->email }}" class="text-decoration-none">
                                        <i class="fas fa-envelope"></i> {{ $employee->email }}
                                    </a>
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Telefono</label>
                                <p class="fw-bold">
                                    <a href="tel:{{ $employee->telefono }}" class="text-decoration-none">
                                        <i class="fas fa-phone"></i> {{ $employee->telefono }}
                                    </a>
                                </p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Data Creazione</label>
                                <p class="fw-bold">{{ $employee->created_at->format('d/m/Y H:i') }}</p>
                            </div>
                        </div>
                    </div>
                    
                    @if($employee->updated_at != $employee->created_at)
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Ultimo Aggiornamento</label>
                                <p class="fw-bold">{{ $employee->updated_at->format('d/m/Y H:i') }}</p>
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
