@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-6">
            <h1 class="h3 mb-0">Gestione Dipendenti</h1>
            <p class="text-muted">Gestisci i dipendenti dell'agenzia</p>
        </div>
        <div class="col-md-6 text-end">
            <a href="{{ route('employees.create') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Nuovo Dipendente
            </a>
        </div>
    </div>

    <!-- Search and Filter -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="{{ route('employees.index') }}">
                <div class="row">
                    <div class="col-md-4">
                        <input type="text" name="search" class="form-control" placeholder="Cerca per nome, cognome, email..." value="{{ request('search') }}">
                    </div>
                    <div class="col-md-3">
                        <select name="ruolo" class="form-select">
                            <option value="">Tutti i ruoli</option>
                            <option value="Fotografo" {{ request('ruolo') == 'Fotografo' ? 'selected' : '' }}>Fotografo</option>
                            <option value="Editor Video" {{ request('ruolo') == 'Editor Video' ? 'selected' : '' }}>Editor Video</option>
                            <option value="Programmatore Web" {{ request('ruolo') == 'Programmatore Web' ? 'selected' : '' }}>Programmatore Web</option>
                            <option value="Social Media Manager" {{ request('ruolo') == 'Social Media Manager' ? 'selected' : '' }}>Social Media Manager</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <button type="submit" class="btn btn-outline-primary">
                            <i class="fas fa-search"></i> Cerca
                        </button>
                        <a href="{{ route('employees.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i> Reset
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Employees Table -->
    <div class="card">
        <div class="card-body">
            @if($employees->count() > 0)
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Foto</th>
                                <th>Nome</th>
                                <th>Ruolo</th>
                                <th>Email</th>
                                <th>Telefono</th>
                                <th>Azioni</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($employees as $employee)
                            <tr>
                                <td>
                                    @if($employee->foto)
                                        <img src="{{ asset('storage/' . $employee->foto) }}" alt="Foto" class="rounded-circle" width="40" height="40">
                                    @else
                                        <div class="profile-photo-placeholder">
                                            {{ strtoupper(substr($employee->nome, 0, 1)) }}
                                        </div>
                                    @endif
                                </td>
                                <td>{{ $employee->full_name }}</td>
                                <td>
                                    <span class="badge bg-info">{{ $employee->ruolo }}</span>
                                </td>
                                <td>{{ $employee->email }}</td>
                                <td>{{ $employee->telefono }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('employees.show', $employee) }}" class="btn btn-sm btn-outline-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('employees.edit', $employee) }}" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form action="{{ route('employees.generate-password', $employee) }}" method="POST" class="d-inline">
                                            @csrf
                                            <button type="submit" class="btn btn-sm btn-outline-warning" onclick="return confirm('Generare una nuova password per questo dipendente?')">
                                                <i class="fas fa-key"></i>
                                            </button>
                                        </form>
                                        <form action="{{ route('employees.destroy', $employee) }}" method="POST" class="d-inline">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-sm btn-outline-danger" onclick="return confirm('Sei sicuro di voler eliminare questo dipendente?')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-center">
                    {{ $employees->links() }}
                </div>
            @else
                <div class="text-center py-4">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5>Nessun dipendente trovato</h5>
                    <p class="text-muted">Non ci sono dipendenti che corrispondono ai criteri di ricerca.</p>
                    <a href="{{ route('employees.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Aggiungi il primo dipendente
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
